# 开发规范和规则

- 微信小程序webview页面导航错误已修复：1)webview.vue去除TypeScript语法，使用正确的onLoad生命周期；2)创建fix-webview-navigation.bat清理脚本；3)注释auth.vue中未定义的recordOperation函数调用。编译时webview页面丢失主要由TS语法错误导致。
- API文件结构已优化：1)将server/api.js的路径常量定义合并到utils/api.js中；2)删除server/api.js文件，减少文件冗余；3)保持api.user.xxx()等调用方式不变，确保向后兼容；4)统一API管理在utils/api.js一个文件中，包含路径定义和调用方法。
- 用户要求移除系统中所有到user-info页面的跳转，采用方案B：保留路由配置但禁用所有跳转逻辑，登录成功后直接跳转到首页或个人中心
- 登录数据存储问题修复：1)用户存储方法需要保存完整的user_info数据，包括is_staff等关键字段；2)移除登录页面的直接uni.setStorageSync调用，统一通过userStore管理；3)确保API层和用户存储层的数据一致性
