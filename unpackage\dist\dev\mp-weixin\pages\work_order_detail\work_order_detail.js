"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  __name: "work_order_detail",
  setup(__props) {
    const caseNumber = common_vendor.ref("");
    const initiateDate = common_vendor.ref("");
    const closeDate = common_vendor.ref("");
    const caseStatus = common_vendor.ref("");
    const workOrderData = common_vendor.ref([]);
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:94", "页面参数:", options);
      if (options.case_number) {
        caseNumber.value = options.case_number;
        common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:99", "接收到调解ID:", caseNumber.value);
      }
      if (options.initiate_date) {
        initiateDate.value = options.initiate_date;
        common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:104", "接收到日期:", initiateDate.value);
      }
      if (options.close_date) {
        closeDate.value = options.close_date;
        common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:110", "关闭日期:", closeDate.value);
      }
      if (options.case_status_cn) {
        caseStatus.value = options.case_status_cn;
        common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:116", "接收到调解状态:", caseStatus.value);
        if (caseStatus.value === "已关闭") {
          workOrderData.value.case_status_cn = caseStatus.value;
          common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:123", "检测到已关闭状态，已更新workOrderData状态为:", caseStatus.value);
        }
      }
      if (caseNumber.value) {
        fetchWorkOrderDetail(caseNumber.value);
      }
    });
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:135", "工单详情页面组件已挂载");
    });
    const fetchWorkOrderDetail = (id) => {
      if (id) {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        utils_api.api.workOrder.getDetail(id).then((res) => {
          common_vendor.index.hideLoading();
          if (res.state === "success") {
            workOrderData.value = res.data;
          } else {
            common_vendor.index.showToast({
              title: res.msg || "获取调解确认失败",
              icon: "none"
            });
          }
        }).catch((err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/work_order_detail/work_order_detail.vue:161", "获取调解确认失败", err);
          common_vendor.index.showToast({
            title: "获取调解确认失败",
            icon: "none"
          });
        });
      }
    };
    const handleAccept = () => {
      common_vendor.index.showModal({
        title: "确认接受",
        content: "您确定要接受此调解吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            if (caseNumber.value) {
              utils_api.api.workOrder.acceptWorkOrder(caseNumber.value).then((res2) => {
                common_vendor.index.hideLoading();
                if (res2.state === "success") {
                  common_vendor.index.showToast({
                    title: res2.msg,
                    icon: "success",
                    duration: 1500
                  });
                  setTimeout(() => {
                    common_vendor.index.navigateTo({
                      url: `/pages/solution_confirm/solution_confirm?caseNumber=${workOrderData.value.id}`,
                      success: () => {
                        common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:200", "跳转到调解方案确认页面");
                      },
                      fail: (err) => {
                        common_vendor.index.__f__("error", "at pages/work_order_detail/work_order_detail.vue:203", "跳转失败", err);
                        common_vendor.index.showToast({
                          title: "跳转失败",
                          icon: "none"
                        });
                      }
                    });
                  }, 1500);
                } else {
                  common_vendor.index.showToast({
                    title: res2.msg || "操作失败",
                    icon: "none"
                  });
                }
              }).catch((err) => {
                common_vendor.index.hideLoading();
                common_vendor.index.__f__("error", "at pages/work_order_detail/work_order_detail.vue:220", "接受调解失败", err);
                common_vendor.index.showToast({
                  title: "接受调解失败",
                  icon: "none"
                });
              });
            } else {
              common_vendor.index.showToast({
                title: "缺少调解案件编号",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const handleReject = () => {
      common_vendor.index.redirectTo({
        url: `/pages/mediation_query/mediation_query`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(caseNumber.value),
        b: common_vendor.t(caseStatus.value),
        c: caseStatus.value === "待确认" ? 1 : "",
        d: common_vendor.t(initiateDate.value),
        e: caseStatus.value === "已关闭"
      }, caseStatus.value === "已关闭" ? {
        f: common_vendor.t(closeDate.value)
      } : {}, {
        g: common_vendor.f(workOrderData.value.mediation_config, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: common_vendor.t(item.value),
            c: item.id
          };
        }),
        h: common_vendor.f(workOrderData.value.attachments, (file, k0, i0) => {
          return {
            a: common_vendor.n(file.name.split(".").pop() === "pdf" ? "fa-file-pdf" : "fa-file-image"),
            b: file.name.split(".").pop() === "pdf" ? "#ff4d4f" : "#52c41a",
            c: common_vendor.t(file.name),
            d: file.id
          };
        }),
        i: workOrderData.value.case_status_cn === "已关闭"
      }, workOrderData.value.case_status_cn === "已关闭" ? {
        j: common_vendor.t(workOrderData.value.closingReason || "调解案件已超过规定期限。")
      } : {
        k: common_vendor.o(handleAccept),
        l: common_vendor.o(handleReject)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/work_order_detail/work_order_detail.js.map
